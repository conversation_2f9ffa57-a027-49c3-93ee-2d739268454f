; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html


; Uncomment the example to be developed or compiled
[platformio]
default_envs = xmc4700_relax_kit
;src_dir = examples/readAngleSpeedRevolutions
;src_dir = examples/readAngleTest/readAngleTest.ino,
;src_dir = examples/readAngleValueProcessing,
;src_dir = examples/readMultipleRegisters,
;src_dir = examples/readSpeedProcessing,
;src_dir = examples/sensorRegisters,
;src_dir = examples/sensorType,
;src_dir = examples/testSensorMainValues,
;src_dir = examples/useMultipleSensors,
;src_dir = examples/writeRegisters

lib_dir = .

[env:xmc1100_xmc2go]
platform = infineonxmc
board = xmc1100_xmc2go
framework = arduino
lib_deps = Wire
monitor_speed = 115200

[env:xmc1100_boot_kit]
platform = infineonxmc
board = xmc1100_boot_kit
framework = arduino
lib_deps = Wire
monitor_speed = 115200

[env:xmc4700_relax_kit]
platform = infineonxmc
board = xmc4700_relax_kit
framework = arduino
lib_deps = Wire
monitor_speed = 115200

[env:uno]
platform = atmelavr
board = uno
framework = arduino
lib_deps = Wire
monitor_speed = 115200
