name: Release

on:
  push:
    tags: 
      - '[0-9]+.[0-9]+.[0-9]+**'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version'
        required: true
        default: ''
        type: choice
        options:
          - patch
          - minor
          - major

jobs:
  arduino-devops:
    uses: Infineon/arduino-devops/.github/workflows/release.yml@latest
    with:
      version: ${{ inputs.version }}
    secrets:
      release-ssh-key: ${{ secrets.release_wflow_key }}